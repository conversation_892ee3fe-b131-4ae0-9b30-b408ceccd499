# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a containerized WordPress development environment using Docker. The setup includes:

- **WordPress**: Custom Docker image based on PHP 8.2 with Apache
- **MySQL**: Database backend (version 8.0)
- **Docker Compose**: Orchestrates the multi-container setup

## Common Commands

### Development Environment
```bash
# Start the development environment
docker-compose up -d

# Stop the environment (WARNING: This removes wp-config.php)
docker-compose down

# Restart containers (preserves wp-config.php)
docker-compose restart

# Rebuild containers after changes
docker-compose up --build

# View logs
docker-compose logs -f wordpress
docker-compose logs -f db

# Recreate wp-config.php if lost after docker-compose down
docker-compose exec --user root wordpress bash -c "cat > wp-config.php << 'EOF' \
<?php \
define('DB_NAME', 'wordpress');\
define('DB_USER', 'wordpress');\
define('DB_PASSWORD', 'wordpress');\
define('DB_HOST', 'db');\
define('DB_CHARSET', 'utf8mb4');\
define('DB_COLLATE', '');\
define('AUTH_KEY', 'your-unique-auth-key-here');\
define('SECURE_AUTH_KEY', 'your-unique-secure-auth-key-here');\
define('LOGGED_IN_KEY', 'your-unique-logged-in-key-here');\
define('NONCE_KEY', 'your-unique-nonce-key-here');\
define('AUTH_SALT', 'your-unique-auth-salt-here');\
define('SECURE_AUTH_SALT', 'your-unique-secure-auth-salt-here');\
define('LOGGED_IN_SALT', 'your-unique-logged-in-salt-here');\
define('NONCE_SALT', 'your-unique-nonce-salt-here');\
\$table_prefix = 'wp_';\
define('WP_DEBUG', false);\
if (!defined('ABSPATH')) define('ABSPATH', dirname(__FILE__) . '/');\
require_once(ABSPATH . 'wp-settings.php');\
EOF" && docker-compose exec --user root wordpress sed -i 's/\\!/!/g' wp-config.php && docker-compose exec --user root wordpress chown www-data:www-data wp-config.php
```

### Access Points
- **WordPress Site**: http://localhost:8080
- **MySQL Database**: localhost:3306 (from host)
  - Database: wordpress
  - User: wordpress
  - Password: wordpress

## Architecture

### Container Structure
- **WordPress container**: Runs PHP 8.2 + Apache with WordPress installed
- **Database container**: MySQL 8.0 with persistent storage
- **Volume mounts**: `wp-content` directory is mounted for theme/plugin development

### Key Configuration
- **PHP settings**: Configured via `uploads.ini` (512M memory, 64M upload limit)
- **WordPress files**: Automatically downloaded during container build
- **Database persistence**: Uses Docker volume `db_data`

### File Structure
- `Dockerfile`: WordPress container configuration
- `docker-compose.yml`: Multi-container orchestration
- `uploads.ini`: PHP upload/memory limits
- `wp-content/`: WordPress themes, plugins, and uploads (mounted volume)