(function (blocks, element, editor, components) {
    const { registerBlockType } = blocks;
    const { createElement: el, Fragment } = element;
    const { InspectorControls, MediaUpload, MediaUploadCheck, RichText, ColorPalette } = editor;
    const { PanelBody, Button, TextControl, SelectControl } = components;

    // FVG Hero Block
    registerBlockType('custom-theme/fvg-hero', {
        title: 'FVG Hero Section',
        icon: 'format-image',
        category: 'layout',
        attributes: {
            title: {
                type: 'string',
                default: 'Na Saúde de Todos'
            },
            subtitle: {
                type: 'string',
                default: ''
            },
            imageUrl: {
                type: 'string',
                default: ''
            },
            backgroundColor: {
                type: 'string',
                default: 'white'
            }
        },
        edit: function (props) {
            const { attributes, setAttributes } = props;
            const { title, subtitle, imageUrl, backgroundColor } = attributes;

            return el(Fragment, {},
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Hero Settings' },
                        el(TextControl, {
                            label: 'Title',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            }
                        }),
                        el(TextControl, {
                            label: 'Subtitle',
                            value: subtitle,
                            onChange: function (value) {
                                setAttributes({ subtitle: value });
                            }
                        }),
                        el('p', {}, 'Background Color:'),
                        el(ColorPalette, {
                            value: backgroundColor,
                            onChange: function (value) {
                                setAttributes({ backgroundColor: value });
                            }
                        }),
                        el(MediaUploadCheck, {},
                            el(MediaUpload, {
                                onSelect: function (media) {
                                    setAttributes({ imageUrl: media.url });
                                },
                                allowedTypes: ['image'],
                                render: function (obj) {
                                    return el(Button, {
                                        className: imageUrl ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                        onClick: obj.open
                                    }, imageUrl ? 'Change Image' : 'Upload Image');
                                }
                            })
                        )
                    )
                ),
                el('div', {
                    className: 'fvg-hero-editor',
                    style: { backgroundColor: backgroundColor, padding: '40px', position: 'relative', minHeight: '400px', color: '#00D084', border: '2px dashed #00D084' }
                },
                    el('div', {
                        style: {
                            maxWidth: '1280px',
                            margin: '0 auto',
                            minHeight: '320px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }
                    },
                        // Flex layout preview matching the actual CSS
                        el('div', {
                            style: {
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '40px',
                                width: '100%'
                            }
                        },
                            el('div', {
                                style: {
                                    fontSize: '48px',
                                    fontWeight: 'bold',
                                    color: '#00D084',
                                    textAlign: 'right',
                                    whiteSpace: 'nowrap'
                                }
                            }, title.split(' ').slice(0, 2).join(' ')),
                            imageUrl && el('div', { style: { flexShrink: '0' } },
                                el('img', {
                                    src: imageUrl,
                                    style: {
                                        maxWidth: '300px',
                                        height: 'auto',
                                        display: 'block'
                                    }
                                })
                            ),
                            title.split(' ').length > 2 && el('div', {
                                style: {
                                    fontSize: '48px',
                                    fontWeight: 'bold',
                                    color: '#00D084',
                                    textAlign: 'left',
                                    whiteSpace: 'nowrap'
                                }
                            }, title.split(' ').slice(2).join(' '))
                        )
                    ),
                    subtitle && el('div', { style: { textAlign: 'center', marginTop: '20px' } },
                        el(RichText, {
                            tagName: 'p',
                            value: subtitle,
                            onChange: function (value) {
                                setAttributes({ subtitle: value });
                            },
                            placeholder: 'Enter subtitle...',
                            style: { fontSize: '18px', margin: '0', color: '#00D084' }
                        })
                    ),
                    el('div', { style: { position: 'absolute', top: '10px', left: '10px' } },
                        el(RichText, {
                            tagName: 'span',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            },
                            placeholder: 'Enter hero title...',
                            style: { fontSize: '14px', opacity: '0.7', color: '#00D084' }
                        })
                    )
                )
            );
        },
        save: function () {
            return null; // Server-side rendering
        }
    });

    // FVG Feature Block
    registerBlockType('custom-theme/fvg-feature', {
        title: 'FVG Feature Section',
        icon: 'layout',
        category: 'layout',
        attributes: {
            title: {
                type: 'string',
                default: 'Feature Title'
            },
            content: {
                type: 'string',
                default: 'Feature description...'
            },
            buttonText: {
                type: 'string',
                default: 'Learn More'
            },
            buttonUrl: {
                type: 'string',
                default: '#'
            },
            iconName: {
                type: 'string',
                default: 'pharmacy'
            },
            backgroundColor: {
                type: 'string',
                default: '#00D084'
            },
            textColor: {
                type: 'string',
                default: '#ffffff'
            },
            alignment: {
                type: 'string',
                default: 'left'
            }
        },
        edit: function (props) {
            const { attributes, setAttributes } = props;
            const { title, content, buttonText, buttonUrl, iconName, backgroundColor, textColor, alignment } = attributes;

            return el(Fragment, {},
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Feature Settings' },
                        el(TextControl, {
                            label: 'Title',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            }
                        }),
                        el(TextControl, {
                            label: 'Button Text',
                            value: buttonText,
                            onChange: function (value) {
                                setAttributes({ buttonText: value });
                            }
                        }),
                        el(SelectControl, {
                            label: 'Button Page',
                            value: buttonUrl,
                            options: [
                                { label: 'Select a page...', value: '' },
                                ...(window.fvgPages ? window.fvgPages : [])
                            ],
                            onChange: function (value) {
                                setAttributes({ buttonUrl: value });
                            }
                        }),
                        el(SelectControl, {
                            label: 'Content Alignment',
                            value: alignment,
                            options: [
                                { label: 'Left', value: 'left' },
                                { label: 'Right', value: 'right' }
                            ],
                            onChange: function (value) {
                                setAttributes({ alignment: value });
                            }
                        }),
                        el('p', {}, 'Background Color:'),
                        el(ColorPalette, {
                            value: backgroundColor,
                            onChange: function (value) {
                                setAttributes({ backgroundColor: value });
                            }
                        }),
                        el('p', {}, 'Text Color:'),
                        el(ColorPalette, {
                            value: textColor,
                            onChange: function (value) {
                                setAttributes({ textColor: value });
                            }
                        }),
                        el(SelectControl, {
                            label: 'Icon',
                            value: iconName,
                            options: [
                                { label: 'None', value: '' },
                                ...(window.fvgIcons ? window.fvgIcons.list : [
                                    { label: 'Pharmacy', value: 'pharmacy' },
                                    { label: 'Doctor', value: 'doctor' },
                                    { label: 'Delivery', value: 'delivery' },
                                    { label: 'Phone', value: 'phone' },
                                    { label: 'Heart', value: 'heart' },
                                    { label: 'Location', value: 'location' }
                                ])
                            ],
                            onChange: function (value) {
                                setAttributes({ iconName: value });
                            }
                        })
                    )
                ),
                el('div', {
                    className: 'fvg-feature-editor',
                    style: {
                        backgroundColor: backgroundColor,
                        color: textColor,
                        padding: '40px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: alignment === 'right' ? 'row-reverse' : 'row'
                    }
                },
                    el('div', { style: { flex: '1', marginRight: alignment === 'left' ? '40px' : '0', marginLeft: alignment === 'right' ? '40px' : '0' } },
                        el(RichText, {
                            tagName: 'h2',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            },
                            placeholder: 'Enter feature title...',
                            style: { fontSize: '32px', fontWeight: 'bold', marginBottom: '20px' }
                        }),
                        el(RichText, {
                            tagName: 'p',
                            value: content,
                            onChange: function (value) {
                                setAttributes({ content: value });
                            },
                            placeholder: 'Enter feature description...',
                            style: { fontSize: '16px', lineHeight: '1.6', marginBottom: '20px' }
                        }),
                        el('a', {
                            href: '#',
                            style: {
                                display: 'inline-block',
                                padding: '12px 24px',
                                border: '2px solid currentColor',
                                borderRadius: '25px',
                                textDecoration: 'none',
                                color: 'inherit',
                                fontWeight: 'bold'
                            }
                        }, buttonText)
                    ),
                    iconName && el('div', {
                        style: {
                            width: '120px',
                            height: '120px',
                            borderRadius: '50%',
                            border: '3px solid currentColor',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexShrink: 0
                        }
                    },
                        el('div', {
                            style: {
                                width: '60px',
                                height: '60px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '12px',
                                color: 'currentColor',
                                textAlign: 'center'
                            }
                        }, iconName.charAt(0).toUpperCase() + iconName.slice(1))
                    )
                )
            );
        },
        save: function () {
            return null; // Server-side rendering
        }
    });

    // FVG Contact Block
    registerBlockType('custom-theme/fvg-contact', {
        title: 'FVG Contact Section',
        icon: 'email',
        category: 'layout',
        attributes: {
            title: {
                type: 'string',
                default: 'Fale Connosco!'
            },
            content: {
                type: 'string',
                default: 'Entre em contacto connosco para mais informações.'
            },
            buttonText: {
                type: 'string',
                default: 'Contacte-nos'
            },
            buttonUrl: {
                type: 'string',
                default: '#contact'
            },
            imageUrl: {
                type: 'string',
                default: ''
            },
            backgroundColor: {
                type: 'string',
                default: '#ffffff'
            },
            textColor: {
                type: 'string',
                default: '#00D084'
            }
        },
        edit: function (props) {
            const { attributes, setAttributes } = props;
            const { title, content, buttonText, buttonUrl, imageUrl, backgroundColor, textColor } = attributes;

            return el(Fragment, {},
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Contact Settings' },
                        el(TextControl, {
                            label: 'Title',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            }
                        }),
                        el(TextControl, {
                            label: 'Button Text',
                            value: buttonText,
                            onChange: function (value) {
                                setAttributes({ buttonText: value });
                            }
                        }),
                        el(SelectControl, {
                            label: 'Button Page',
                            value: buttonUrl,
                            options: [
                                { label: 'Select a page...', value: '' },
                                ...(window.fvgPages ? window.fvgPages : [])
                            ],
                            onChange: function (value) {
                                setAttributes({ buttonUrl: value });
                            }
                        }),
                        el(MediaUploadCheck, {},
                            el(MediaUpload, {
                                onSelect: function (media) {
                                    setAttributes({ imageUrl: media.url });
                                },
                                allowedTypes: ['image'],
                                render: function (obj) {
                                    return el(Button, {
                                        className: imageUrl ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                        onClick: obj.open
                                    }, imageUrl ? 'Change Image' : 'Upload Image');
                                }
                            })
                        ),
                        el('p', {}, 'Background Color:'),
                        el(ColorPalette, {
                            value: backgroundColor,
                            onChange: function (value) {
                                setAttributes({ backgroundColor: value });
                            }
                        }),
                        el('p', {}, 'Text Color:'),
                        el(ColorPalette, {
                            value: textColor,
                            onChange: function (value) {
                                setAttributes({ textColor: value });
                            }
                        })
                    )
                ),
                el('div', {
                    className: 'fvg-contact-editor',
                    style: {
                        backgroundColor: backgroundColor,
                        color: textColor,
                        padding: '40px',
                        textAlign: 'center'
                    }
                },
                    imageUrl && el('div', { style: { marginBottom: '30px' } },
                        el('img', {
                            src: imageUrl,
                            alt: title,
                            style: { maxWidth: '120px', height: 'auto' }
                        })
                    ),
                    el('div', { style: { maxWidth: '600px', margin: '0 auto' } },
                        el(RichText, {
                            tagName: 'h2',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            },
                            placeholder: 'Enter contact title...',
                            style: { fontSize: '32px', fontWeight: 'bold', marginBottom: '20px' }
                        }),
                        el(RichText, {
                            tagName: 'p',
                            value: content,
                            onChange: function (value) {
                                setAttributes({ content: value });
                            },
                            placeholder: 'Enter contact description...',
                            style: { fontSize: '16px', lineHeight: '1.6', marginBottom: '30px' }
                        }),
                        el('div', { style: { textAlign: 'center' } },
                            el('div', {
                                style: {
                                    display: 'inline-block',
                                    width: '60px',
                                    height: '60px',
                                    borderRadius: '50%',
                                    border: '2px solid currentColor',
                                    marginBottom: '30px',
                                    position: 'relative'
                                }
                            },
                                el('span', {
                                    style: {
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        fontSize: '24px'
                                    }
                                }, '+')
                            )
                        ),
                        el('a', {
                            href: '#',
                            style: {
                                display: 'inline-block',
                                padding: '12px 24px',
                                border: '2px solid currentColor',
                                borderRadius: '25px',
                                textDecoration: 'none',
                                color: 'inherit',
                                fontWeight: 'bold'
                            }
                        }, buttonText)
                    )
                )
            );
        },
        save: function () {
            return null; // Server-side rendering
        }
    });

    // FVG Image Feature Block
    registerBlockType('custom-theme/fvg-image-feature', {
        title: 'FVG Image Feature Section',
        icon: 'format-image',
        category: 'layout',
        attributes: {
            title: {
                type: 'string',
                default: 'Feature Title'
            },
            content: {
                type: 'string',
                default: 'Feature description...'
            },
            imageUrl: {
                type: 'string',
                default: ''
            },
            backgroundColor: {
                type: 'string',
                default: '#00D084'
            },
            textColor: {
                type: 'string',
                default: '#ffffff'
            },
            alignment: {
                type: 'string',
                default: 'left'
            }
        },
        edit: function (props) {
            const { attributes, setAttributes } = props;
            const { title, content, imageUrl, backgroundColor, textColor, alignment } = attributes;

            return el(Fragment, {},
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Settings' },
                        el(SelectControl, {
                            label: 'Alignment',
                            value: alignment,
                            options: [
                                { label: 'Left', value: 'left' },
                                { label: 'Right', value: 'right' }
                            ],
                            onChange: function (value) {
                                setAttributes({ alignment: value });
                            }
                        }),
                        el('p', {}, 'Background Color:'),
                        el(ColorPalette, {
                            value: backgroundColor,
                            onChange: function (value) {
                                setAttributes({ backgroundColor: value });
                            }
                        }),
                        el('p', {}, 'Text Color:'),
                        el(ColorPalette, {
                            value: textColor,
                            onChange: function (value) {
                                setAttributes({ textColor: value });
                            }
                        }),
                        el('p', {}, 'Feature Image:'),
                        el(MediaUploadCheck, {},
                            el(MediaUpload, {
                                onSelect: function (media) {
                                    setAttributes({ imageUrl: media.url });
                                },
                                allowedTypes: ['image'],
                                render: function (obj) {
                                    return el(Button, {
                                        className: imageUrl ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                        onClick: obj.open
                                    }, imageUrl ? 'Change Image' : 'Upload Image');
                                }
                            })
                        )
                    )
                ),
                el('div', {
                    className: 'fvg-image-feature-editor',
                    style: {
                        backgroundColor: backgroundColor,
                        color: textColor,
                        padding: '40px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '40px',
                        flexDirection: alignment === 'right' ? 'row-reverse' : 'row'
                    }
                },
                    el('div', { style: { flex: 1 } },
                        el(RichText, {
                            tagName: 'h2',
                            value: title,
                            onChange: function (value) {
                                setAttributes({ title: value });
                            },
                            placeholder: 'Enter feature title...',
                            style: { fontSize: '28px', fontWeight: 'bold', marginBottom: '16px' }
                        }),
                        el(RichText, {
                            tagName: 'p',
                            value: content,
                            onChange: function (value) {
                                setAttributes({ content: value });
                            },
                            placeholder: 'Enter feature description...',
                            style: { fontSize: '16px', lineHeight: '1.6', marginBottom: '24px' }
                        })
                    ),
                    imageUrl && el('div', {
                        style: {
                            width: '200px',
                            height: '200px',
                            flexShrink: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }
                    },
                        el('img', {
                            src: imageUrl,
                            alt: title,
                            style: {
                                maxWidth: '100%',
                                maxHeight: '100%',
                                objectFit: 'contain'
                            }
                        })
                    )
                )
            );
        },
        save: function () {
            return null; // Server-side rendering
        }
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor || window.wp.editor,
    window.wp.components
);
