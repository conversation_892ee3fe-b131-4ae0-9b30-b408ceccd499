/* FVG Hero Block Styles */
.fvg-hero {
    position: relative;
    padding: 80px 0;
    overflow: hidden;
    background: white !important;
    color: #00D084 !important;
}

.fvg-hero__container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fvg-hero__content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    width: 100%;
}

.fvg-hero__title-left,
.fvg-hero__title-right {
    font-size: 48px;
    font-weight: bold;
    color: #00D084 !important;
    margin: 0;
    line-height: 1.2;
    white-space: nowrap;
}

.fvg-hero__title-left {
    text-align: right;
}

.fvg-hero__title-right {
    text-align: left;
}

.fvg-hero__image {
    flex-shrink: 0;
}

.fvg-hero__image img {
    max-width: 400px;
    height: auto;
    display: block;
}

.fvg-hero__subtitle {
    font-size: 18px;
    color: #00D084 !important;
    margin: 20px 0 0 0;
    opacity: 0.9;
    text-align: center;
}

.fvg-hero__decoration {
    position: absolute;
    right: 80px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
}

.fvg-hero__circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
}

.fvg-hero__cross {
    fill: white;
}

/* FVG Feature Block Styles */
.fvg-feature {
    padding: 80px 0;
}

.fvg-feature__container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 60px;
}

.fvg-feature--right .fvg-feature__container {
    flex-direction: row-reverse;
}

.fvg-feature__content {
    flex: 1;
    max-width: 500px;
}

.fvg-feature__title {
    font-size: 36px;
    font-weight: bold;
    margin: 0 0 24px 0;
    line-height: 1.3;
}

.fvg-feature__text {
    font-size: 16px;
    line-height: 1.6;
    margin: 0 0 32px 0;
    opacity: 0.9;
}

.fvg-feature__button {
    display: inline-block;
    padding: 14px 28px;
    border: 2px solid currentColor;
    border-radius: 30px;
    text-decoration: none;
    color: inherit;
    font-weight: bold;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.fvg-feature__button:hover {
    background-color: currentColor;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.fvg-feature__icon {
    flex-shrink: 0;
}

.fvg-feature__icon-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid currentColor;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.fvg-feature__icon-circle:hover {
    transform: scale(1.05);
}

.fvg-feature__icon svg {
    width: 70px;
    height: 70px;
    fill: currentColor;
    stroke: currentColor;
    stroke-width: 2;
}

/* White background variant */
.fvg-feature--white {
    background-color: #f8f9fa !important;
    color: #333 !important;
}

.fvg-feature--white .fvg-feature__button:hover {
    background-color: #333;
    color: white;
}

/* FVG Contact Block Styles */
.fvg-contact {
    padding: 80px 0;
    text-align: center;
}

.fvg-contact__container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

.fvg-contact__title {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 20px;
}

.fvg-contact__text {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 30px;
}

.fvg-contact__icon {
    display: inline-block;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid currentColor;
    margin-bottom: 30px;
    position: relative;
}

.fvg-contact__icon:before {
    content: '+';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
}

.fvg-contact__button {
    display: inline-block;
    padding: 12px 24px;
    border: 2px solid currentColor;
    border-radius: 25px;
    text-decoration: none;
    color: inherit;
    font-weight: bold;
    transition: all 0.3s ease;
}

.fvg-contact__button:hover {
    background-color: currentColor;
    color: #fff;
}

.fvg-contact__image {
    margin-bottom: 30px;
}

.fvg-contact__image img {
    max-width: 120px;
    height: auto;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .fvg-hero {
        padding: 60px 0;
    }
    
    .fvg-hero__container {
        flex-direction: column;
        min-height: auto;
        text-align: center;
    }
    
    .fvg-hero__content {
        flex-direction: column;
        gap: 30px;
    }
    
    .fvg-hero__title-left,
    .fvg-hero__title-right {
        font-size: 36px;
        white-space: normal;
        text-align: center;
        display: inline;
    }
    
    .fvg-hero__title-left:after {
        content: " ";
    }
    
    .fvg-hero__image {
        order: 2;
    }
    
    .fvg-hero__decoration {
        display: none;
    }
    
    .fvg-hero__circle {
        width: 150px;
        height: 150px;
    }
    
    .fvg-feature {
        padding: 60px 0;
    }
    
    .fvg-feature__container {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }
    
    .fvg-feature--right .fvg-feature__container {
        flex-direction: column;
    }
    
    .fvg-feature__title {
        font-size: 28px;
    }
    
    .fvg-feature__icon-circle {
        width: 120px;
        height: 120px;
    }
    
    .fvg-feature__icon svg {
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 480px) {
    .fvg-hero__title {
        font-size: 28px;
    }
    
    .fvg-hero__image img {
        max-width: 300px;
    }
    
    .fvg-feature__title {
        font-size: 24px;
    }
}

@media (max-width: 768px) {
    .fvg-contact {
        padding: 60px 0;
    }
    
    .fvg-contact__title {
        font-size: 28px;
    }
}
