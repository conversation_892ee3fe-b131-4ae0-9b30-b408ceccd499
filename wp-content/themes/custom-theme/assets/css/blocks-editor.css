/* Editor-specific styles for FVG blocks */

.fvg-hero-editor {
    border: 2px dashed #00D084;
    border-radius: 8px;
}

.fvg-feature-editor {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-height: 200px;
}

.fvg-feature-editor .wp-block-rich-text__editable {
    background: transparent !important;
    color: inherit !important;
}

.fvg-hero-editor .wp-block-rich-text__editable {
    background: transparent !important;
    color: #00D084 !important;
}

.wp-block-rich-text__editable[data-placeholder]:empty:before {
    color: inherit;
    opacity: 0.6;
}