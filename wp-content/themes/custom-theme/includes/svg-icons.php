<?php
// Predefined SVG icons for FVG blocks

function get_fvg_svg_icons() {
    return array(
        'pharmacy' => '<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <path d="M45 15 L45 45 L15 45 L15 55 L45 55 L45 85 L55 85 L55 55 L85 55 L85 45 L55 45 L55 15 Z" fill="currentColor"/>
        </svg>',
        
        'doctor' => '<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <circle cx="50" cy="25" r="12" fill="none" stroke="currentColor" stroke-width="3"/>
            <path d="M50 40 L35 45 L35 55 L30 55 L30 85 L70 85 L70 55 L65 55 L65 45 Z" fill="none" stroke="currentColor" stroke-width="3"/>
            <path d="M42 62 L48 68 L58 58" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>',
        
        'delivery' => '<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <rect x="10" y="40" width="50" height="30" rx="5" fill="none" stroke="currentColor" stroke-width="3"/>
            <rect x="60" y="50" width="20" height="20" rx="3" fill="none" stroke="currentColor" stroke-width="3"/>
            <circle cx="25" cy="75" r="8" fill="none" stroke="currentColor" stroke-width="3"/>
            <circle cx="70" cy="75" r="8" fill="none" stroke="currentColor" stroke-width="3"/>
            <path d="M45 25 L55 25 L55 35 L65 35 L65 45 L55 45 L55 55 L45 55 L45 45 L35 45 L35 35 L45 35 Z" fill="currentColor"/>
        </svg>',
        
        'phone' => '<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <rect x="25" y="10" width="50" height="80" rx="8" fill="none" stroke="currentColor" stroke-width="3"/>
            <rect x="30" y="20" width="40" height="55" rx="2" fill="none" stroke="currentColor" stroke-width="2"/>
            <circle cx="50" cy="83" r="3" fill="currentColor"/>
            <circle cx="45" cy="50" r="8" fill="none" stroke="currentColor" stroke-width="2"/>
            <circle cx="55" cy="40" r="6" fill="none" stroke="currentColor" stroke-width="2"/>
            <circle cx="55" cy="60" r="4" fill="none" stroke="currentColor" stroke-width="2"/>
        </svg>',
        
        'heart' => '<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <path d="M50 85 C25 70 15 45 25 25 C30 15 45 15 50 30 C55 15 70 15 75 25 C85 45 75 70 50 85 Z" fill="none" stroke="currentColor" stroke-width="3" stroke-linejoin="round"/>
            <path d="M40 40 L45 45 L60 30" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>',
        
        'location' => '<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <path d="M50 15 C35 15 25 25 25 40 C25 65 50 85 50 85 C50 85 75 65 75 40 C75 25 65 15 50 15 Z" fill="none" stroke="currentColor" stroke-width="3"/>
            <circle cx="50" cy="40" r="8" fill="none" stroke="currentColor" stroke-width="3"/>
        </svg>'
    );
}

// Function to get icon HTML by name
function get_fvg_icon($icon_name) {
    $icons = get_fvg_svg_icons();
    return isset($icons[$icon_name]) ? $icons[$icon_name] : '';
}