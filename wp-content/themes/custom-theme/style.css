/*
Theme Name: Custom Theme
Description: A custom WordPress theme for your website
Author: Your Name
Version: 1.0
License: GPL v2 or later
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

body.page {
    margin: 0;
    padding: 0;
    padding-bottom: 0;
}

/* Header Styles */
.site-header {
    background: white;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

/* Logo Styles */
.header-logo {
    flex-shrink: 0;
}

.header-logo a {
    display: block;
    text-decoration: none;
}

.header-logo img {
    max-height: 50px;
    width: 100%;
}

.header-logo-link {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.header-logo-link:hover {
    opacity: 0.8;
}

.header-logo-icon {
    flex-shrink: 0;
}

.header-logo-svg {
    width: 40px;
    height: 40px;
    display: block;
}

.header-logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.header-logo-title {
    font-size: 20px;
    font-weight: bold;
    color: #00D084;
    margin: 0;
}

.header-logo-subtitle {
    font-size: 10px;
    color: #666;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Navigation Styles */
.header-nav {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.header-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 30px;
}

.header-menu li {
    margin: 0;
}

.header-menu a {
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 700;
    padding: 10px 0;
    transition: color 0.3s ease;
    position: relative;
    text-transform: uppercase;
}

.header-menu a:hover,
.header-menu .current-menu-item a,
.header-menu .current_page_item a {
    color: #00D084;
}

.header-menu a:hover::after,
.header-menu .current-menu-item a::after,
.header-menu .current_page_item a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #00D084;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    gap: 4px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: #333;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover .hamburger-line {
    background: #00D084;
}

/* Main Content */
main {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Content container for regular content */
.content-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 2rem 20px;
}

.post, .page {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.post-title, .page-title {
    color: #333;
    margin-bottom: 1rem;
}

.post-meta {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.post-content, .page-content {
    line-height: 1.8;
}

/* Hide page title on homepage */
.home .page-title,
.front-page .page-title {
    display: none;
}

/* Footer */
.site-footer {
    background: #333;
    color: white;
    margin-top: 2rem;
}

/* Footer Container */
.footer-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px;
    background: #333;
}

/* First Row: Logo Section */
.footer-logo-section {
    padding: 40px 0;
}

.footer-logo a {
    display: inline-block;
}

.footer-logo img {
    max-height: 60px;
    width: auto;
}

.footer-logo-text,
.footer-social-text {
    font-size: 32px;
    font-weight: bold;
    color: #00D084;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.footer-social-text {
    font-size: 48px;
    line-height: 1.1;
    margin-bottom: 1em;
}

.footer-logo-text:hover {
    opacity: 0.8;
}

/* Second Row: Main Content */
.footer-main-section {
    padding: 60px 0;
}

.footer-columns {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 60px;
    align-items: flex-start;
}

/* Address Column */
.footer-address-content p {
    margin-bottom: 8px;
    line-height: 1.4;
    color: #ccc;
    font-size: 14px;
}

.footer-address-content p:last-child {
    margin-bottom: 0;
}

/* Menu Column */
.footer-nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-nav-menu li {
    margin-bottom: 12px;
}

.footer-nav-menu a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.footer-nav-menu a:hover,
.footer-nav-menu .current-menu-item a,
.footer-nav-menu .current_page_item a {
    color: #00D084;
}

/* Social Column */
.footer-social-links {
    display: flex;
    gap: 15px;
}

.footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: #444;
    border-radius: 50%;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social-link:hover {
    background: #00D084;
    color: white;
    transform: translateY(-2px);
}

.footer-social-link svg {
    width: 20px;
    height: 20px;
}

/* Third Row: Bottom Section */
.footer-bottom-section {
    padding: 30px 0;
    border-top: 1px solid #444;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-copyright {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.footer-copyright p {
    margin: 0;
    color: #ccc;
    font-size: 14px;
}

.footer-policy-links {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-policy-link {
    color: #ccc;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-policy-link:hover {
    color: #00D084;
}

.footer-separator {
    color: #666;
}

/* Made by Bruta */
.footer-bruta-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-bruta-link:hover {
    color: #00D084;
}

.footer-bruta-text {
    font-size: 12px;
}

.footer-bruta-logo {
    font-weight: bold;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .content-container {
        padding: 1rem 15px;
    }

    /* Header Responsive */
    .header-container {
        padding: 0 15px;
    }

    .header-content {
        padding: 12px 0;
    }

    .header-logo-svg {
        width: 35px;
        height: 35px;
    }

    .header-logo-title {
        font-size: 18px;
    }

    .header-logo-subtitle {
        font-size: 9px;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .header-nav {
        display: none;
    }

    /* Mobile menu when active */
    .header-nav.mobile-active {
        display: block;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #e5e5e5;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .header-nav.mobile-active .header-menu {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .header-nav.mobile-active .header-menu a {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
        width: 100%;
    }

    /* Footer Responsive */
    .footer-container {
        padding: 0 15px;
    }

    .footer-logo-section {
        padding: 30px 0;
    }

    .footer-logo img {
        max-height: 50px;
    }

    .footer-logo-text {
        font-size: 28px;
    }

    .footer-main-section {
        padding: 40px 0;
    }

    .footer-columns {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-social-links {
        justify-content: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .footer-policy-links {
        justify-content: center;
    }

    .footer-copyright {
        flex-direction: column;
    }

}

@media (max-width: 480px) {
    .content-container {
        padding: 1rem 10px;
    }

    .footer-container {
        padding: 0 10px;
    }

    .footer-logo-section {
        padding: 25px 0;
    }

    .footer-logo-text {
        font-size: 24px;
    }

    .footer-social-text {
        font-size: 32px;
    }

    .footer-main-section {
        padding: 30px 0;
    }

    .footer-columns {
        gap: 30px;
    }

    .footer-column-title {
        font-size: 16px;
    }

    .footer-social-link {
        width: 40px;
        height: 40px;
    }

    .footer-social-link svg {
        width: 18px;
        height: 18px;
    }

    .footer-bottom-section {
        padding: 20px 0;
    }

    .footer-policy-links {
        flex-direction: column;
        gap: 5px;
    }

    .footer-separator {
        display: none;
    }
}