/*
Theme Name: Custom Theme
Description: A custom WordPress theme for your website
Author: Your Name
Version: 1.0
License: GPL v2 or later
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    margin: 0;
    padding: 0;
}

/* Header Styles */
header {
    background: #333;
    color: white;
    padding: 1rem 0;
}

.site-title {
    text-align: center;
    margin: 0;
}

/* Navigation */
nav ul {
    list-style: none;
    text-align: center;
    padding: 1rem 0;
}

nav ul li {
    display: inline;
    margin: 0 1rem;
}

nav ul li a {
    color: white;
    text-decoration: none;
}

nav ul li a:hover {
    text-decoration: underline;
}

/* Main Content */
main {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Content container for regular content */
.content-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 2rem 20px;
}

.post, .page {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.post-title, .page-title {
    color: #333;
    margin-bottom: 1rem;
}

.post-meta {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.post-content, .page-content {
    line-height: 1.8;
}

/* Footer */
footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .content-container {
        padding: 1rem 15px;
    }
    
    nav ul li {
        display: block;
        margin: 0.5rem 0;
    }
}

@media (max-width: 480px) {
    .content-container {
        padding: 1rem 10px;
    }
}