<?php
// Include SVG icons
require_once get_template_directory() . '/includes/svg-icons.php';

// Theme setup
function custom_theme_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => 'Primary Menu',
        'footer'  => 'Footer Menu'
    ));
}
add_action('after_setup_theme', 'custom_theme_setup');

// Enqueue styles and scripts
function custom_theme_scripts() {
    wp_enqueue_style('custom-theme-style', get_stylesheet_uri());
    wp_enqueue_style('custom-blocks-style', get_template_directory_uri() . '/assets/css/blocks.css', array(), '1.0');
    wp_enqueue_script('custom-header-js', get_template_directory_uri() . '/assets/js/header.js', array(), '1.0', true);
}
add_action('wp_enqueue_scripts', 'custom_theme_scripts');

// Register custom blocks
function register_custom_blocks() {
    // Register FVG Hero Block
    register_block_type('custom-theme/fvg-hero', array(
        'render_callback' => 'render_fvg_hero_block',
        'attributes' => array(
            'title' => array(
                'type' => 'string',
                'default' => 'Na Saúde de Todos'
            ),
            'subtitle' => array(
                'type' => 'string',
                'default' => ''
            ),
            'imageUrl' => array(
                'type' => 'string',
                'default' => ''
            ),
            'backgroundColor' => array(
                'type' => 'string',
                'default' => 'white'
            )
        )
    ));
    
    // Register FVG Feature Section Block
    register_block_type('custom-theme/fvg-feature', array(
        'render_callback' => 'render_fvg_feature_block',
        'attributes' => array(
            'title' => array(
                'type' => 'string',
                'default' => 'Feature Title'
            ),
            'content' => array(
                'type' => 'string',
                'default' => 'Feature description...'
            ),
            'buttonText' => array(
                'type' => 'string',
                'default' => 'Learn More'
            ),
            'buttonUrl' => array(
                'type' => 'string',
                'default' => '#'
            ),
            'iconName' => array(
                'type' => 'string',
                'default' => 'pharmacy'
            ),
            'backgroundColor' => array(
                'type' => 'string',
                'default' => '#00D084'
            ),
            'textColor' => array(
                'type' => 'string',
                'default' => '#ffffff'
            ),
            'alignment' => array(
                'type' => 'string',
                'default' => 'left'
            )
        )
    ));
    
    // Register FVG Contact Section Block
    register_block_type('custom-theme/fvg-contact', array(
        'render_callback' => 'render_fvg_contact_block',
        'attributes' => array(
            'title' => array(
                'type' => 'string',
                'default' => 'Fale Connosco!'
            ),
            'content' => array(
                'type' => 'string',
                'default' => 'Entre em contacto connosco para mais informações.'
            ),
            'buttonText' => array(
                'type' => 'string',
                'default' => 'Contacte-nos'
            ),
            'buttonUrl' => array(
                'type' => 'string',
                'default' => '#contact'
            ),
            'imageUrl' => array(
                'type' => 'string',
                'default' => ''
            ),
            'backgroundColor' => array(
                'type' => 'string',
                'default' => '#ffffff'
            ),
            'textColor' => array(
                'type' => 'string',
                'default' => '#00D084'
            )
        )
    ));

    // Register FVG Image Feature Section Block
    register_block_type('custom-theme/fvg-image-feature', array(
        'render_callback' => 'render_fvg_image_feature_block',
        'attributes' => array(
            'title' => array(
                'type' => 'string',
                'default' => 'Feature Title'
            ),
            'content' => array(
                'type' => 'string',
                'default' => 'Feature description...'
            ),
            'imageUrl' => array(
                'type' => 'string',
                'default' => ''
            ),
            'backgroundColor' => array(
                'type' => 'string',
                'default' => '#00D084'
            ),
            'textColor' => array(
                'type' => 'string',
                'default' => '#ffffff'
            ),
            'alignment' => array(
                'type' => 'string',
                'default' => 'left'
            )
        )
    ));

}
add_action('init', 'register_custom_blocks');

// Enqueue block editor assets
function custom_blocks_editor_assets() {
    wp_enqueue_script(
        'custom-blocks-editor',
        get_template_directory_uri() . '/assets/js/blocks.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components'),
        '1.0',
        true
    );
    
    // Pass icon list to JavaScript
    $icon_list = array();
    foreach (get_fvg_svg_icons() as $key => $svg) {
        $icon_list[] = array(
            'label' => ucfirst(str_replace('_', ' ', $key)),
            'value' => $key
        );
    }
    
    wp_localize_script('custom-blocks-editor', 'fvgIcons', array(
        'list' => $icon_list
    ));
    
    wp_enqueue_style(
        'custom-blocks-editor-style',
        get_template_directory_uri() . '/assets/css/blocks-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );
}
add_action('enqueue_block_editor_assets', 'custom_blocks_editor_assets');

// Render FVG Hero Block
function render_fvg_hero_block($attributes) {
    $title = $attributes['title'] ?? 'Na Saúde de Todos';
    $subtitle = $attributes['subtitle'] ?? '';
    $imageUrl = $attributes['imageUrl'] ?? '';
    $backgroundColor = $attributes['backgroundColor'] ?? 'white';
    
    // Split title for desktop layout
    $title_parts = explode(' ', $title);
    $title_left = '';
    $title_right = '';
    
    if (count($title_parts) >= 3) {
        $title_left = implode(' ', array_slice($title_parts, 0, 2)); // "Na Saúde"
        $title_right = implode(' ', array_slice($title_parts, 2)); // "de Todos"
    } else {
        $title_left = $title;
    }
    
    ob_start();
    ?>
    <section class="fvg-hero" style="background-color: <?php echo esc_attr($backgroundColor); ?>">
        <div class="fvg-hero__container">
            <div class="fvg-hero__content">
                <div class="fvg-hero__title-left"><?php echo esc_html($title_left); ?></div>
                <?php if ($imageUrl): ?>
                    <div class="fvg-hero__image">
                        <img src="<?php echo esc_url($imageUrl); ?>" alt="<?php echo esc_attr($title); ?>">
                    </div>
                <?php endif; ?>
                <?php if ($title_right): ?>
                    <div class="fvg-hero__title-right"><?php echo esc_html($title_right); ?></div>
                <?php endif; ?>
            </div>
            <?php if ($subtitle): ?>
                <p class="fvg-hero__subtitle"><?php echo esc_html($subtitle); ?></p>
            <?php endif; ?>
        </div>
    </section>
    <?php
    return ob_get_clean();
}

// Render FVG Feature Block
function render_fvg_feature_block($attributes) {
    $title = $attributes['title'] ?? 'Feature Title';
    $content = $attributes['content'] ?? 'Feature description...';
    $buttonText = $attributes['buttonText'] ?? 'Learn More';
    $buttonUrl = $attributes['buttonUrl'] ?? '#';
    $iconName = $attributes['iconName'] ?? 'pharmacy';
    $backgroundColor = $attributes['backgroundColor'] ?? '#00D084';
    $textColor = $attributes['textColor'] ?? '#ffffff';
    $alignment = $attributes['alignment'] ?? 'left';
    
    // Get the SVG icon from the predefined list
    $iconSvg = get_fvg_icon($iconName);
    
    ob_start();
    ?>
    <section class="fvg-feature fvg-feature--<?php echo esc_attr($alignment); ?>" 
             style="background-color: <?php echo esc_attr($backgroundColor); ?>; color: <?php echo esc_attr($textColor); ?>">
        <div class="fvg-feature__container">
            <div class="fvg-feature__content">
                <h2 class="fvg-feature__title"><?php echo esc_html($title); ?></h2>
                <p class="fvg-feature__text"><?php echo esc_html($content); ?></p>
                <a href="<?php echo esc_url($buttonUrl); ?>" class="fvg-feature__button">
                    <?php echo esc_html($buttonText); ?>
                </a>
            </div>
            <?php if ($iconName && $iconSvg): ?>
                <div class="fvg-feature__icon">
                    <div class="fvg-feature__icon-circle">
                        <?php 
                        // Debug: Let's see what we're getting
                        // echo '<!-- Icon Name: ' . esc_html($iconName) . ' -->';
                        // echo '<!-- Icon SVG: ' . esc_html($iconSvg) . ' -->';
                        
                        // Output SVG directly - WordPress might be stripping it
                        echo $iconSvg; 
                        ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Debug: Icon not showing. Name: <?php echo esc_html($iconName); ?>, SVG: <?php echo esc_html($iconSvg ? 'present' : 'empty'); ?> -->
            <?php endif; ?>
        </div>
    </section>
    <?php
    return ob_get_clean();
}

// Render FVG Contact Block
function render_fvg_contact_block($attributes) {
    $title = $attributes['title'] ?? 'Fale Connosco!';
    $content = $attributes['content'] ?? 'Entre em contacto connosco para mais informações.';
    $buttonText = $attributes['buttonText'] ?? 'Contacte-nos';
    $buttonUrl = $attributes['buttonUrl'] ?? '#contact';
    $imageUrl = $attributes['imageUrl'] ?? '';
    $backgroundColor = $attributes['backgroundColor'] ?? '#ffffff';
    $textColor = $attributes['textColor'] ?? '#00D084';

    ob_start();
    ?>
    <section class="fvg-contact" style="background-color: <?php echo esc_attr($backgroundColor); ?>; color: <?php echo esc_attr($textColor); ?>">
        <div class="fvg-contact__container">
            <?php if ($imageUrl): ?>
                <div class="fvg-contact__image">
                    <img src="<?php echo esc_url($imageUrl); ?>" alt="<?php echo esc_attr($title); ?>">
                </div>
            <?php endif; ?>
            <h2 class="fvg-contact__title"><?php echo esc_html($title); ?></h2>
            <p class="fvg-contact__text"><?php echo esc_html($content); ?></p>
            <a href="<?php echo esc_url($buttonUrl); ?>" class="fvg-contact__button">
                <?php echo esc_html($buttonText); ?>
            </a>
        </div>
    </section>
    <?php
    return ob_get_clean();
}

// Render FVG Image Feature Block
function render_fvg_image_feature_block($attributes) {
    $title = $attributes['title'] ?? 'Feature Title';
    $content = $attributes['content'] ?? 'Feature description...';
    $imageUrl = $attributes['imageUrl'] ?? '';
    $backgroundColor = $attributes['backgroundColor'] ?? '#00D084';
    $textColor = $attributes['textColor'] ?? '#ffffff';
    $alignment = $attributes['alignment'] ?? 'left';

    ob_start();
    ?>
    <section class="fvg-image-feature fvg-image-feature--<?php echo esc_attr($alignment); ?>"
             style="background-color: <?php echo esc_attr($backgroundColor); ?>; color: <?php echo esc_attr($textColor); ?>">
        <div class="fvg-image-feature__container">
            <div class="fvg-image-feature__content">
                <h2 class="fvg-image-feature__title"><?php echo esc_html($title); ?></h2>
                <p class="fvg-image-feature__text"><?php echo esc_html($content); ?></p>
            </div>
            <?php if ($imageUrl): ?>
                <div class="fvg-image-feature__image">
                    <img src="<?php echo esc_url($imageUrl); ?>" alt="<?php echo esc_attr($title); ?>">
                </div>
            <?php endif; ?>
        </div>
    </section>
    <?php
    return ob_get_clean();
}



// Register widget areas
function custom_theme_widgets_init() {
    register_sidebar(array(
        'name'          => 'Sidebar',
        'id'            => 'sidebar-1',
        'description'   => 'Add widgets here to appear in your sidebar',
        'before_widget' => '<div class="widget">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => 'Footer',
        'id'            => 'footer-1',
        'description'   => 'Add widgets here to appear in your footer',
        'before_widget' => '<div class="footer-widget">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'custom_theme_widgets_init');

// Header fallback menu
function header_fallback_menu() {
    echo '<ul class="header-menu">';
    echo '<li><a href="' . home_url() . '">Início</a></li>';
    echo '<li><a href="' . home_url('/sobre-nos') . '">Sobre Nós</a></li>';
    echo '<li><a href="' . home_url('/servicos') . '">Serviços</a></li>';
    echo '<li><a href="' . home_url('/encomendas') . '">Encomendas</a></li>';
    echo '<li><a href="' . home_url('/farmacias-de-servico') . '">Farmácias de Serviço</a></li>';
    echo '<li><a href="' . home_url('/contactos') . '">Contactos</a></li>';
    echo '</ul>';
}

// Footer fallback menu
function footer_fallback_menu() {
    echo '<ul class="footer-nav-menu">';
    echo '<li><a href="' . home_url() . '">Início</a></li>';
    echo '<li><a href="' . home_url('/sobre-nos') . '">Sobre Nós</a></li>';
    echo '<li><a href="' . home_url('/servicos') . '">Serviços</a></li>';
    echo '<li><a href="' . home_url('/encomendas') . '">Encomendas</a></li>';
    echo '<li><a href="' . home_url('/farmacias-de-servico') . '">Farmácias de Serviço</a></li>';
    echo '<li><a href="' . home_url('/contactos') . '">Contactos</a></li>';
    echo '</ul>';
}

// Custom excerpt length
function custom_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'custom_excerpt_length');

// Custom excerpt more text
function custom_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'custom_excerpt_more');
