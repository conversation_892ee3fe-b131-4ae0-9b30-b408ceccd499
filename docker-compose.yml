services:
  wordpress:
    build: .
    ports:
      - "8080:80"
    environment:
      WORDPRE<PERSON>_DB_HOST: db
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
    volumes:
      - ./wp-content:/var/www/html/wp-content:delegated
      - ./uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
    user: "${UID:-1000}:${GID:-1000}"
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
      MYSQL_ROOT_PASSWORD: rootpassword
    volumes:
      - db_data:/var/lib/mysql
    ports:
      - "3306:3306"

volumes:
  db_data: